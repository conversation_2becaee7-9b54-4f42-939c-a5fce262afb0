package com.dell.it.hip.util.logging;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import com.dell.it.hip.config.ConfigRef;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.strategy.flows.EDIFlowStepStrategy;
import com.emc.it.eis.activity.transaction.monitoring.logging.exporter.sender.ActmonService;
//import com.emc.it.eis.transaction.monitoring.logging.msgbuilder.domain.WiretapEvent;

import jakarta.annotation.PostConstruct;

@Service
public class WiretapService {

    private static final Logger logger = LoggerFactory.getLogger(WiretapService.class);
    private final ThreadPoolTaskExecutor executor;
    
    @Autowired
	private ActmonService actmonService;
    
    @Autowired
    private EDIFlowStepStrategy testflow;

    @Autowired
    @Qualifier("hip.error") // Make sure this bean exists (DirectChannel or ExecutorChannel)
    private MessageChannel hipErrorChannel;
    // Constructor injection (recommended for Spring)
    public WiretapService(@Qualifier("hipWiretapExecutor") ThreadPoolTaskExecutor executor) {
        this.executor = executor;
    }
    @PostConstruct
    public void subscribeToErrorChannel() {
        if (hipErrorChannel instanceof org.springframework.messaging.SubscribableChannel sc) {
            sc.subscribe(this::handleErrorMessage);
            logger.info("WiretapService: Subscribed to hip.error channel for error event logging.");
        } else {
            logger.warn("WiretapService: hip.error channel is not subscribable.");
        }
    }

    // This method is called whenever a message is sent to hip.error channel
    public void handleErrorMessage(Message<?> message) {
        logger.error("[WIRETAP][ERROR] Received error message: headers={}, payload={}",
                message.getHeaders(), message.getPayload());
        // Optionally, you can create a WiretapEvent and call doWiretap if you want
        // more structured logging/auditing.
    }


    public void tap(Message<?> message,
                    HIPIntegrationDefinition def,
                    ConfigRef ref,
                    String eventType,
                    String description) {
        try {
        	String payload = new String((byte[]) message.getPayload(), StandardCharsets.UTF_8);
        	testflow.testEDI(message);
        	logger.error("[WIRETAP][ERROR] Received error message: headers={}, payload={}",
                    message.getHeaders(), payload);
        	//WiretapEvent event = createEvent(message, def, ref, eventType, description);
            //executor.submit(() -> doWiretap(event));
        } catch (Exception ex) {
           // logger.warn("Wiretap event dropped due to full queue:{}", event, ex);
        }
    }
    
    public void tapIntegrationLifecycleEvent(HIPIntegrationDefinition def, String eventType,
            String description) {
    	logger.error("Integration Lifecycle Event {} occured for integration {} with service maanger {}",eventType, def.getHipIntegrationName(), def.getServiceManagerName());
               
    }


    /* private void doWiretap(WiretapEvent wire_event) {
        try {
            logger.info("[WIRETAP][{}] integration={}, version={}, flowId={}, type={}, configRef={}, msgId={}, desc={}",
            		wire_event.toString());
            actmonService.sendActmonEvent(wire_event);
            logger.info("Actmon Logging Message posted successfully!");
        } catch (Exception e) {
            logger.error("WiretapService error: {}", e.getMessage(), e);
        }
	}

    private WiretapEvent createEvent(Message<?> message, HIPIntegrationDefinition def, ConfigRef ref, String eventType, String description) {	
		  WiretapEvent wire_event = new WiretapEvent(); 
		  wire_event.setTimestamp(Instant.now());
		  wire_event.setEventType(eventType);
		  wire_event.setDescription(description);
		  wire_event.setIntegrationName(def != null ? def.getHipIntegrationName() : null);
		  wire_event.setIntegrationVersion(def != null ? def.getVersion() : null);
		  wire_event.setBusinessFlowType(def != null ? def.getBusinessFlowType() : null);
		  wire_event.setHipIntegrationType(def != null ? def.getHipIntegrationType() : null); 
		  wire_event.setBusinessFlowVersion(def != null ? def.getBusinessFlowVersion() : null); 
		  wire_event.setBusinessFlowName(def != null ? def.getBusinessFlowName() : null); 
		  wire_event.setHandlerType(ref != null ? ref.getType() : null); 
		  wire_event.setHandlerConfigRef(ref != null ? ref.getId() : null); 
		  wire_event.setMessageId(message != null && message.getHeaders().containsKey("id") ? message.getHeaders().get("id").toString() : null); 
		  wire_event.setPayload(message != null ? new String((byte[]) message.getPayload(), StandardCharsets.UTF_8) : null);
		  wire_event.setHeaders(message != null ? message.getHeaders() : null);	 
        return wire_event;
    }*/

}