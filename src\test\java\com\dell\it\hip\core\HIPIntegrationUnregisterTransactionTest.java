package com.dell.it.hip.core;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.dell.it.hip.config.HIPClusterEvent;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.core.registry.HIPIntegrationRegistry;
import com.dell.it.hip.exception.IntegrationNotFoundException;
import com.dell.it.hip.exception.IntegrationOperationException;
import com.dell.it.hip.strategy.adapters.InputAdapterStrategy;
import com.dell.it.hip.util.logging.WiretapService;

/**
 * Test class to verify the transaction management and sequencing fixes
 * for the unregister endpoint issues.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationUnregisterTransactionTest {

    @Mock
    private ServiceManager serviceManager;
    
    @Mock
    private HIPIntegrationRegistry hipIntegrationRegistry;
    
    @Mock
    private HIPIntegrationRuntimeService hipIntegrationRuntimeService;
    
    @Mock
    private HIPClusterCoordinationService clusterCoordinationService;
    
    @Mock
    private WiretapService wiretapService;
    
    @Mock
    private InputAdapterStrategy inputAdapterStrategy;
    
    @InjectMocks
    private HIPIntegrationOrchestrationService orchestrationService;
    
    private HIPIntegrationDefinition testDefinition;
    private final String serviceManagerName = "test-service-manager";
    private final String integrationName = "tprsvc-mq-xyz-abc-3";
    private final String version = "1.0";

    @BeforeEach
    void setUp() {
        // Set up test data
        testDefinition = new HIPIntegrationDefinition();
        testDefinition.setHipIntegrationName(integrationName);
        testDefinition.setVersion(version);
        testDefinition.setServiceManagerName(serviceManagerName);
        
        AdapterConfigRef adapterRef = new AdapterConfigRef();
        adapterRef.setId("adapter1");
        adapterRef.setType("kafka");
        testDefinition.setAdapterConfigRefs(List.of(adapterRef));
        
        // Set the serviceManagerName field using reflection
        ReflectionTestUtils.setField(orchestrationService, "serviceManagerName", serviceManagerName);
        
        // Mock ServiceManager behavior (lenient to avoid unnecessary stubbing warnings)
        lenient().when(serviceManager.getInputAdapterStrategyMap()).thenReturn(
            Collections.singletonMap("kafka", inputAdapterStrategy)
        );
    }

    @Test
    void testUnregisterIntegration_Success() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(integrationName, version))
            .thenReturn(testDefinition);
        
        // Act
        assertDoesNotThrow(() -> {
            orchestrationService.unregisterHIPIntegration(integrationName, version);
        });
        
        // Assert - verify the correct sequence of operations
        verify(serviceManager).getIntegrationDefinition(integrationName, version);
        verify(inputAdapterStrategy).shutdown(testDefinition);
        verify(clusterCoordinationService).broadcastUnregistration(testDefinition);
        verify(serviceManager).unregisterIntegration(integrationName, version);
        verify(hipIntegrationRegistry).deleteByServiceManagerNameAndHipIntegrationNameAndVersion(
            serviceManagerName, integrationName, version);
        verify(hipIntegrationRuntimeService).updateHIPIntegrationStatus(
            serviceManagerName, integrationName, version, IntegrationStatus.UNREGISTERED);
        verify(wiretapService).tapIntegrationLifecycleEvent(testDefinition, "UnRegistered", "");
    }

    @Test
    void testUnregisterIntegration_IntegrationNotFound() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(integrationName, version))
            .thenReturn(null);
        
        // Act & Assert
        IntegrationNotFoundException exception = assertThrows(
            IntegrationNotFoundException.class,
            () -> orchestrationService.unregisterHIPIntegration(integrationName, version)
        );
        
        assertTrue(exception.getMessage().contains(integrationName));
        assertTrue(exception.getMessage().contains(version));
        
        // Verify that no cleanup operations were attempted
        verify(inputAdapterStrategy, never()).shutdown(any());
        verify(clusterCoordinationService, never()).broadcastUnregistration(any());
        verify(serviceManager, never()).unregisterIntegration(anyString(), anyString());
        verify(hipIntegrationRegistry, never()).deleteByServiceManagerNameAndHipIntegrationNameAndVersion(
            anyString(), anyString(), anyString());
    }

    @Test
    void testUnregisterIntegration_AdapterShutdownFailure() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(integrationName, version))
            .thenReturn(testDefinition);
        doThrow(new RuntimeException("Adapter shutdown failed"))
            .when(inputAdapterStrategy).shutdown(testDefinition);
        
        // Act & Assert
        IntegrationOperationException exception = assertThrows(
            IntegrationOperationException.class,
            () -> orchestrationService.unregisterHIPIntegration(integrationName, version)
        );
        
        assertEquals("unregister", exception.getOperation());
        assertEquals(integrationName, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
        
        // Verify that the failure was logged and wiretapped
        verify(wiretapService).tapIntegrationLifecycleEvent(
            eq(testDefinition), eq("UnRegister Failed"), anyString());
    }

    @Test
    void testOnClusterEvent_NullDefinitionHandling() {
        // Arrange - simulate a cluster event for an unregistered integration
        HIPClusterEvent event = HIPClusterEvent.forAdapterTarget(
            "PAUSE", integrationName, version, "adapter1", Collections.emptyMap());
        
        when(serviceManager.getIntegrationDefinition(integrationName, version))
            .thenReturn(null);
        
        // Act - should not throw NPE
        assertDoesNotThrow(() -> {
            orchestrationService.onClusterEvent(event);
        });
        
        // Assert - verify no adapter operations were attempted
        verify(inputAdapterStrategy, never()).pause(any(), any());
        verify(inputAdapterStrategy, never()).resume(any(), any());
    }

    @Test
    void testOnClusterEvent_UnregisteredEventHandling() {
        // Arrange
        HIPClusterEvent event = HIPClusterEvent.forAdapterTarget(
            "UNREGISTERED", integrationName, version, "adapter1", Collections.emptyMap());
        
        when(serviceManager.getIntegrationDefinition(integrationName, version))
            .thenReturn(null);
        
        // Act - should handle gracefully
        assertDoesNotThrow(() -> {
            orchestrationService.onClusterEvent(event);
        });
        
        // Assert - verify no operations were attempted
        verify(inputAdapterStrategy, never()).pause(any(), any());
        verify(inputAdapterStrategy, never()).resume(any(), any());
    }

    @Test
    void testUnregisterSequence_BroadcastBeforeServiceManagerRemoval() {
        // This test verifies that broadcastUnregistration is called BEFORE
        // serviceManager.unregisterIntegration to prevent NPE in cluster event processing
        
        // Arrange
        when(serviceManager.getIntegrationDefinition(integrationName, version))
            .thenReturn(testDefinition);
        
        List<String> operationOrder = new ArrayList<>();
        
        doAnswer(invocation -> {
            operationOrder.add("broadcastUnregistration");
            return null;
        }).when(clusterCoordinationService).broadcastUnregistration(testDefinition);
        
        doAnswer(invocation -> {
            operationOrder.add("unregisterIntegration");
            return null;
        }).when(serviceManager).unregisterIntegration(integrationName, version);
        
        // Act
        orchestrationService.unregisterHIPIntegration(integrationName, version);
        
        // Assert - verify correct order
        assertEquals(2, operationOrder.size());
        assertEquals("broadcastUnregistration", operationOrder.get(0));
        assertEquals("unregisterIntegration", operationOrder.get(1));
    }
}
