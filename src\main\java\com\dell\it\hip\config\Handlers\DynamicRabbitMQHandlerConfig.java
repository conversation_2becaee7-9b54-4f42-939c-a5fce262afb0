package com.dell.it.hip.config.Handlers;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DynamicRabbitMQHandlerConfig extends HandlerConfig{
    private String id;                 // Unique propertyRef for this handler
    
    @JsonProperty("rabbitmq.producer.host")
    private String host;
    
	@JsonProperty("rabbitmq.producer.port")
    private Integer port;
	
	@JsonProperty("rabbitmq.producer.username")
    private String username;
	
	@JsonProperty("rabbitmq.producer.password")
    private String password;
    
	@JsonProperty("rabbitmq.producer.vhost")
    private String virtualHost;

    @JsonProperty("rabbitmq.producer.exchange")
    private String exchange;

    @JsonProperty("rabbitmq.producer.routingKey")
    private String routingKey;

    @JsonProperty("rabbitmq.producer.mandatory")
    private Boolean mandatory;

    @JsonProperty("rabbitmq.producer.persistent")
    private Boolean persistent;

    @JsonProperty("rabbitmq.producer.gzipEnabled")
    private Boolean gzipEnabled;

	@JsonProperty("rabbitmq.producer.ssl.enabled")
    private Boolean sslEnabled;

    @JsonProperty("rabbitmq.producer.parameters")
    private Map<String, Object> parameters;

    public Boolean getSslEnabled() {
		return sslEnabled;
	}
	public void setSslEnabled(Boolean sslEnabled) {
		this.sslEnabled = sslEnabled;
	}

    // Getters and setters for all fields
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getHost() { return host; }
    public void setHost(String host) { this.host = host; }

    public Integer getPort() { return port; }
    public void setPort(Integer port) { this.port = port; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public String getVirtualHost() { return virtualHost; }
    public void setVirtualHost(String virtualHost) { this.virtualHost = virtualHost; }

    public String getExchange() { return exchange; }
    public void setExchange(String exchange) { this.exchange = exchange; }

    public String getRoutingKey() { return routingKey; }
    public void setRoutingKey(String routingKey) { this.routingKey = routingKey; }

    public Boolean getMandatory() { return mandatory; }
    public void setMandatory(Boolean mandatory) { this.mandatory = mandatory; }

    public Boolean getPersistent() { return persistent; }
    public void setPersistent(Boolean persistent) { this.persistent = persistent; }

    public Boolean getGzipEnabled() { return gzipEnabled; }
    public void setGzipEnabled(Boolean gzipEnabled) { this.gzipEnabled = gzipEnabled; }

    public Map<String, Object> getParameters() { return parameters; }
    public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
}