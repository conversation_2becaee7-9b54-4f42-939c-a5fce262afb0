package com.dell.it.hip.config.Handlers;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DynamicIbmmqHandlerConfig extends HandlerConfig {
    private String id;
    
    @JsonProperty("ibmmq.producer.queueManager")
    private String queueManager;

    @JsonProperty("ibmmq.producer.host")
    private String host;

    @JsonProperty("ibmmq.producer.port")
    private Integer port;

    @JsonProperty("ibmmq.producer.channel")
    private String channel;

    @JsonProperty("ibmmq.producer.queue")
    private String queue;

    @JsonProperty("ibmmq.producer.username")
    private String username;

    @JsonProperty("ibmmq.producer.password")
    private String password;

    @JsonProperty("ibmmq.producer.ccsid")
    private Integer ccsid;         // Character set ID (e.g. 1208 for UTF-8, 819 for ISO-8859-1)

    @JsonProperty("ibmmq.producer.encoding")
    private Integer encoding;      // Message encoding (e.g. MQENC_NATIVE)

    @JsonProperty("ibmmq.producer.persistent")
    private Boolean persistent;

    @JsonProperty("ibmmq.producer.gzipEnabled")
    private Boolean gzipEnabled;

    @JsonProperty("ibmmq.producer.auth.type")
    private String authenticationType;

    @JsonProperty("ibmmq.producer.connName")
    private String connName;

    @JsonProperty("ibmmq.producer.sslCipherSuite")
    private String cipherSuite;

    public String getCipherSuite() {
		return cipherSuite;
	}
	public void setCipherSuite(String cipherSuite) {
		this.cipherSuite = cipherSuite;
	}
	public String getConnName() {
		return connName;
	}
	public void setConnName(String connName) {
		this.connName = connName;
	}
	public String getAuthenticationType() {
		return authenticationType;
	}
	public void setAuthenticationType(String authenticationType) {
		this.authenticationType = authenticationType;
	}
	// Getters and setters...
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getQueueManager() { return queueManager; }
    public void setQueueManager(String queueManager) { this.queueManager = queueManager; }

    public String getHost() { return host; }
    public void setHost(String host) { this.host = host; }

    public Integer getPort() { return port; }
    public void setPort(Integer port) { this.port = port; }

    public String getChannel() { return channel; }
    public void setChannel(String channel) { this.channel = channel; }

    public String getQueue() { return queue; }
    public void setQueue(String queue) { this.queue = queue; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public Integer getCcsid() { return ccsid; }
    public void setCcsid(Integer ccsid) { this.ccsid = ccsid; }

    public Integer getEncoding() { return encoding; }
    public void setEncoding(Integer encoding) { this.encoding = encoding; }

    public Boolean getPersistent() { return persistent; }
    public void setPersistent(Boolean persistent) { this.persistent = persistent; }

    public Boolean getGzipEnabled() { return gzipEnabled; }
    public void setGzipEnabled(Boolean gzipEnabled) { this.gzipEnabled = gzipEnabled; }
}