package com.dell.it.hip.core;

import java.util.ArrayList;
import java.util.Deque;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.core.task.TaskExecutor;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dell.it.hip.config.HIPClusterEvent;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.config.FlowSteps.FlowStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.StrictOrderConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.config.rules.RuleEnabledStepConfig;
import com.dell.it.hip.config.rules.RuleRef;
import com.dell.it.hip.core.registry.HIPIntegrationRegistry;
import com.dell.it.hip.exception.IntegrationNotFoundException;
import com.dell.it.hip.exception.IntegrationOperationException;
import com.dell.it.hip.exception.IntegrationRegistrationException;
import com.dell.it.hip.strategy.adapters.DynamicSFTPInputAdapter;
import com.dell.it.hip.strategy.adapters.InputAdapterStrategy;
import com.dell.it.hip.strategy.flows.FlowStepStrategy;
import com.dell.it.hip.strategy.flows.StrictOrderProcessorFlowStepStrategy;
import com.dell.it.hip.strategy.flows.rules.RuleCache;
import com.dell.it.hip.strategy.handlers.HandlerStrategy;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.logging.WiretapService;

import jakarta.annotation.PostConstruct;

@Service
public class HIPIntegrationOrchestrationService {

    private static final Logger logger = LoggerFactory.getLogger(HIPIntegrationOrchestrationService.class);

    @Autowired private HIPIntegrationMapper hipIntegrationMapper;
    @Autowired private RuleCache ruleCache;
    @Autowired private HIPIntegrationRegistry hipIntegrationRegistry;
    @Autowired private HIPIntegrationRuntimeService hipIntegrationRuntimeService;
    @Autowired private HIPClusterCoordinationService clusterCoordinationService;
    @Autowired private ServiceManager serviceManager;
    @Autowired private WiretapService wiretapService;
    @Autowired(required = false) private RetryTemplate retryTemplate;
    @Autowired private StrictOrderProcessorFlowStepStrategy strictOrderProcessorFlowStep;

    @Value("${service.manager.name}")
    private String serviceManagerName;

    private final Deque<HIPClusterEvent> recentClusterEvents = new ConcurrentLinkedDeque<>();
    private static final int MAX_EVENT_HISTORY = 100;
    @Bean("hip.deadLetterChannel")
    public MessageChannel deadLetterChannel() {
        // NullChannel just discards messages, you can replace with a QueueChannel or custom impl for advanced scenarios
        return new org.springframework.integration.channel.NullChannel();
    }
    @PostConstruct
    public void init() {
        logger.info("Initializing all HIPIntegrations at application startup...");
        List<HIPIntegrationRequestEntity> entities = hipIntegrationRegistry.findByServiceManagerName(serviceManagerName);
        for (HIPIntegrationRequestEntity entity : entities) {
        	HIPIntegrationRequest req = null;
            try {
                req = hipIntegrationMapper.toPojo(entity);
                registerInternal(req, false);
            } catch (Exception ex) {
                logger.error("Failed to re-initialize HIPIntegration from entity {}: {}", entity.getHipIntegrationName(), ex.getMessage(), ex);
            }
        }
        clusterCoordinationService.registerClusterEventListener(this::onClusterEvent);
    }

    @Transactional
    public void registerHIPIntegration(HIPIntegrationRequest request) throws IntegrationRegistrationException {
        request.setServiceManagerName(serviceManagerName);
        hipIntegrationRegistry.save(hipIntegrationMapper.toEntity(request));
        registerInternal(request, true);
        logger.info("Registered new HIPIntegration: {}:{}", request.getHipIntegrationName(), request.getVersion());
    }

    private void registerInternal(HIPIntegrationRequest req, boolean broadcast) throws IntegrationRegistrationException {
        try {
            HIPIntegrationDefinition def = hipIntegrationMapper.mapToDefinition(req);

            List<MessageChannel> channels = new ArrayList<>();
            MessageChannel lastChannel = prepareAndWireIntegration(def, channels);
            serviceManager.getInputAdapterStrategyMap().values().forEach(strat -> strat.buildProducers(def));

            serviceManager.getInputAdapterStrategyMap().values().forEach(InputAdapterStrategy::startAll);

            serviceManager.registerIntegration(def, channels);
            hipIntegrationRuntimeService.updateHIPIntegrationStatus(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), IntegrationStatus.RUNNING);

            if (broadcast) {
                clusterCoordinationService.broadcastRegistration(def);
            }
            logger.info("Wired and activated HIPIntegration: {}:{}", def.getHipIntegrationName(), def.getVersion());
            wiretapService.tapIntegrationLifecycleEvent(def, "Registration Successful", "");
        } catch (Exception e) {
            logger.error("Failed to wire and activate HIPIntegration: {}:{}", req.getHipIntegrationName(), req.getVersion(), e);
            HIPIntegrationDefinition def = mapReqToDefinition(req);
            wiretapService.tapIntegrationLifecycleEvent(def, "Registration Failed", e.getMessage());

            // Re-throw as IntegrationRegistrationException to propagate to controller
            throw new IntegrationRegistrationException(req.getHipIntegrationName(), req.getVersion(), e);
        }
    }

    private MessageChannel prepareAndWireIntegration(HIPIntegrationDefinition def, List<MessageChannel> channels) {
        List<FlowStepConfigRef> stepRefs = def.getFlowStepConfigRefs();
        String inputChannelName = def.getHipIntegrationName() + "." + def.getVersion() + ".inputChannel";
        org.springframework.integration.channel.ExecutorChannel inputChannel = new org.springframework.integration.channel.ExecutorChannel((TaskExecutor) serviceManager.getFlowExecutor());
        inputChannel.setComponentName(inputChannelName);
        MessageChannel prev = inputChannel;
        channels.add(inputChannel);

        org.springframework.integration.channel.DirectChannel errorChannel =
                new org.springframework.integration.channel.DirectChannel();
        errorChannel.setComponentName("hip.error");
        channels.add(errorChannel);

        for (int i = 0; i < stepRefs.size(); i++) {
            FlowStepConfigRef stepRef = stepRefs.get(i);
            FlowStepConfig config = def.getConfig(stepRef.getPropertyRef(), FlowStepConfig.class);

            // ----- Rule cache preloading -----
            if (config instanceof RuleEnabledStepConfig ruleConfig) {
                List<RuleRef> ruleRefs = ruleConfig.getRuleRefs();
                String stepType = stepRef.getType();
                if ("FlowRouting".equalsIgnoreCase(stepType)) {
                    ruleCache.refreshIntegrationRules(
                            def.getServiceManagerName(),
                            def.getHipIntegrationName(),
                            def.getVersion()
                    );
                } else if (
                        ("MappingTransformer".equalsIgnoreCase(stepType) ||
                                "FlowTargetsRouting".equalsIgnoreCase(stepType))
                                && ruleRefs != null && !ruleRefs.isEmpty()
                ) {
                    ruleCache.refreshExplicitRules(ruleRefs);
                }
            }

            String stepChannelName = def.getHipIntegrationName() + "." + def.getVersion() + ".flowstepChannel." + i;
            org.springframework.integration.channel.ExecutorChannel stepChannel = new org.springframework.integration.channel.ExecutorChannel((TaskExecutor) serviceManager.getFlowExecutor());
            stepChannel.setComponentName(stepChannelName);
            FlowStepStrategy stepStrategy = serviceManager.getFlowStepStrategy(stepRef.getType());
            bindStepToChannel(prev, stepStrategy, def, stepRef, stepChannel);
            channels.add(stepChannel);
            prev = stepChannel;
        }

        HandlerConfigRef primaryHandlerRef = findHandlerByRole(def, "primary");
        HandlerConfigRef fallbackHandlerRef = findHandlerByRole(def, "fallback");

        if (primaryHandlerRef != null) {
            String handlerChannelName = def.getHipIntegrationName() + "." + def.getVersion() + ".handlerChannel.0";
            org.springframework.integration.channel.ExecutorChannel handlerChannel = new org.springframework.integration.channel.ExecutorChannel((TaskExecutor) serviceManager.getFlowExecutor());
            handlerChannel.setComponentName(handlerChannelName);
            HandlerStrategy handlerStrategy = serviceManager.getHandlerStrategy(primaryHandlerRef.getType());
            bindHandlerToChannelWithRetryAndFallback(prev, handlerStrategy, def, primaryHandlerRef, fallbackHandlerRef, handlerChannel);
            channels.add(handlerChannel);
            prev = handlerChannel;
        }
        return prev;
    }

    private void bindStepToChannel(
            MessageChannel from,
            FlowStepStrategy stepStrategy,
            HIPIntegrationDefinition def,
            FlowStepConfigRef ref,
            MessageChannel to
    ) {
        ((org.springframework.integration.channel.ExecutorChannel) from).subscribe(message -> {
            try {
                List<Message<?>> nextMessages = stepStrategy.executeStep(message, ref, def);
                if (nextMessages != null && !nextMessages.isEmpty()) {
                    for (Message<?> next : nextMessages) {
                        if (next != null) {
                            to.send(next);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("Error in flow step {}: {}", ref.getType(), e.getMessage(), e);
            }
        });
    }

    private void bindHandlerToChannelWithRetryAndFallback(
            MessageChannel from,
            HandlerStrategy handlerStrategy,
            HIPIntegrationDefinition def,
            HandlerConfigRef primaryRef,
            HandlerConfigRef fallbackRef,
            MessageChannel to) {

        ((org.springframework.integration.channel.ExecutorChannel) from).subscribe(message -> {
            boolean handled = false;
            Exception primaryEx = null;
            try {
                if (retryTemplate != null) {
                    retryTemplate.execute(context -> {
                        handlerStrategy.handle(message, def, primaryRef);
                        return null;
                    });
                } else {
                    handlerStrategy.handle(message, def, primaryRef);
                }
                handled = true;
                to.send(message);
            } catch (Exception e) {
                primaryEx = e;
                logger.warn("Primary handler {} failed: {}", primaryRef.getType(), e.getMessage());
            }
            if (!handled && fallbackRef != null) {
                HandlerStrategy fallbackStrategy = serviceManager.getHandlerStrategy(fallbackRef.getType());
                if (fallbackStrategy != null) {
                    try {
                        if (retryTemplate != null) {
                            retryTemplate.execute(context -> {
                                fallbackStrategy.handle(message, def, fallbackRef);
                                return null;
                            });
                        } else {
                            fallbackStrategy.handle(message, def, fallbackRef);
                        }
                        handled = true;
                        to.send(message);
                    } catch (Exception fallbackEx) {
                        logger.error("Fallback handler {} failed: {}", fallbackRef.getType(), fallbackEx.getMessage(), fallbackEx);
                        wiretapService.tap(
                                message,
                                def,
                                fallbackRef,
                                "error",
                                "All output handlers (primary and fallback) failed or paused. Message held for manual reprocessing. Last error: " +
                                        (fallbackEx.getMessage() != null ? fallbackEx.getMessage() : "Unknown error")
                        );
                    }
                } else {
                    logger.error("No fallback handler strategy found for type {}", fallbackRef.getType());
                    wiretapService.tap(
                            message,
                            def,
                            fallbackRef,
                            "error",
                            "Fallback handler not found, message held. Last error: " +
                                    (primaryEx != null && primaryEx.getMessage() != null ? primaryEx.getMessage() : "Unknown error")
                    );
                }
            } else if (!handled) {
                wiretapService.tap(
                        message,
                        def,
                        primaryRef,
                        "error",
                        "Primary output handler failed or paused, and no fallback available. Message held for manual reprocessing. Last error: " +
                                (primaryEx != null && primaryEx.getMessage() != null ? primaryEx.getMessage() : "Unknown error")
                );
            }
        });
    }

    public void processHIPIntegrationMessage(String hipIntegrationName, String version, Message<?> message) {
        MessageChannel inputChannel = serviceManager.getInputChannel(hipIntegrationName, version);
        if (inputChannel == null) {
            logger.warn("No input channel for HIPIntegration: {}:{}", hipIntegrationName, version);
            return;
        }
        inputChannel.send(message);
    }

    public MessageChannel getInputChannel(String hipIntegrationName, String version) {
        return serviceManager.getInputChannel(hipIntegrationName, version);
    }

    @Transactional
    public void unregisterHIPIntegration(String hipIntegrationName, String version)
            throws IntegrationNotFoundException, IntegrationOperationException {
    	try {
            HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
            if (def == null) {
                logger.warn("No definition found during unregister for HIPIntegration: {}:{}", hipIntegrationName, version);
                throw new IntegrationNotFoundException(hipIntegrationName, version);
            }

            // Shutdown adapter strategies first
            serviceManager.getInputAdapterStrategyMap().values().forEach(strat -> {
                try {
                    strat.shutdown(def);
                } catch (Exception e) {
                    logger.error("Error shutting down strategy during unregister: {}", strat.getClass().getSimpleName(), e);
                    wiretapService.tapIntegrationLifecycleEvent(def, "UnRegister Failed", e.getMessage());
                    throw new RuntimeException("Failed to shutdown adapter strategy: " + strat.getClass().getSimpleName(), e);
                }
            });

            // Broadcast unregistration BEFORE removing from ServiceManager to avoid NPE in cluster event processing
            clusterCoordinationService.broadcastUnregistration(def);

            // Remove from ServiceManager (this removes from definitionMap)
            serviceManager.unregisterIntegration(hipIntegrationName, version);

            // Remove from database
            hipIntegrationRegistry.deleteByServiceManagerNameAndHipIntegrationNameAndVersion(serviceManagerName, hipIntegrationName, version);

            // Update runtime status
            hipIntegrationRuntimeService.updateHIPIntegrationStatus(serviceManagerName, hipIntegrationName, version, IntegrationStatus.UNREGISTERED);

            wiretapService.tapIntegrationLifecycleEvent(def, "UnRegistered", "");
            logger.info("Successfully unregistered HIPIntegration: {}:{}", hipIntegrationName, version);
        } catch (IntegrationNotFoundException ex) {
            // Re-throw IntegrationNotFoundException as-is
            throw ex;
        } catch (Exception ex) {
            logger.error("Failed to unregister HIPIntegration: {}:{}, Error: {}", hipIntegrationName, version, ex.getMessage(), ex);
            HIPIntegrationDefinition def = mapInputToDefinition(hipIntegrationName, version, serviceManagerName);
            wiretapService.tapIntegrationLifecycleEvent(def, "UnRegister Failed", ex.getMessage());

            // Re-throw as IntegrationOperationException to propagate to controller
            throw new IntegrationOperationException("unregister", hipIntegrationName, version, ex);
        }
    }

    public void pauseHIPIntegration(String hipIntegrationName, String version)
            throws IntegrationNotFoundException, IntegrationOperationException {
    	try {
    		HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
    		if (def == null) {
                logger.warn("No definition found during pause for HIPIntegration: {}:{}", hipIntegrationName, version);
                throw new IntegrationNotFoundException(hipIntegrationName, version);
            }

            serviceManager.getInputAdapterStrategyMap().values().forEach(strat -> strat.pause(def));
            for (HandlerStrategy strat : serviceManager.getHandlerStrategyMap().values()) {
                for (HandlerConfigRef ref : def.getHandlerConfigRefs()) {
                    strat.pause(def, ref);
                }
            }
            hipIntegrationRuntimeService.updateHIPIntegrationStatus(serviceManagerName, hipIntegrationName, version, IntegrationStatus.PAUSED);
            def.getAdapterConfigRefs().forEach(ref -> clusterCoordinationService.pause(def, ref));
            def.getHandlerConfigRefs().forEach(ref -> clusterCoordinationService.pauseHandler(def, ref));
            logger.info("Paused HIPIntegration: {}:{}", hipIntegrationName, version);
            wiretapService.tapIntegrationLifecycleEvent(def, "Paused", "");
    	} catch (IntegrationNotFoundException ex) {
            // Re-throw IntegrationNotFoundException as-is
            throw ex;
        } catch (Exception ex) {
            logger.error("Failed to Pause HIPIntegration: {}:{}, Error: {}", hipIntegrationName, version, ex.getMessage(), ex);
            HIPIntegrationDefinition def = mapInputToDefinition(hipIntegrationName, version, serviceManagerName);
            wiretapService.tapIntegrationLifecycleEvent(def, "Pause Failed", ex.getMessage());

            // Re-throw as IntegrationOperationException to propagate to controller
            throw new IntegrationOperationException("pause", hipIntegrationName, version, ex);
        }
    }

    public void resumeHIPIntegration(String hipIntegrationName, String version)
            throws IntegrationNotFoundException, IntegrationOperationException {
    	try {
    		HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
    		if (def == null) {
                logger.warn("No definition found during resume for HIPIntegration: {}:{}", hipIntegrationName, version);
                throw new IntegrationNotFoundException(hipIntegrationName, version);
            }

            serviceManager.getInputAdapterStrategyMap().values().forEach(strat -> strat.resume(def));
            for (HandlerStrategy strat : serviceManager.getHandlerStrategyMap().values()) {
                for (HandlerConfigRef ref : def.getHandlerConfigRefs()) {
                    strat.resume(def, ref);
                }
            }
            hipIntegrationRuntimeService.updateHIPIntegrationStatus(serviceManagerName, hipIntegrationName, version, IntegrationStatus.RUNNING);
            def.getAdapterConfigRefs().forEach(ref -> clusterCoordinationService.resume(def, ref));
            def.getHandlerConfigRefs().forEach(ref -> clusterCoordinationService.resumeHandler(def, ref));
            logger.info("Resumed HIPIntegration: {}:{}", hipIntegrationName, version);
            wiretapService.tapIntegrationLifecycleEvent(def, "Resumed", "");
    	} catch (IntegrationNotFoundException ex) {
            // Re-throw IntegrationNotFoundException as-is
            throw ex;
        } catch (Exception ex) {
            logger.error("Failed to Resume HIPIntegration: {}:{}, Error: {}", hipIntegrationName, version, ex.getMessage(), ex);
            HIPIntegrationDefinition def = mapInputToDefinition(hipIntegrationName, version, serviceManagerName);
            wiretapService.tapIntegrationLifecycleEvent(def, "Resume Failed", ex.getMessage());

            // Re-throw as IntegrationOperationException to propagate to controller
            throw new IntegrationOperationException("resume", hipIntegrationName, version, ex);
        }
    }

    // Adapter/Handler control, throttle, cluster events, etc., always use ServiceManager for topology/definition access

    public void applyThrottle(String hipIntegrationName, String version, ThrottleSettings settings) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        def.getAdapterConfigRefs().forEach(ref ->
                clusterCoordinationService.setThrottle(def, ref, settings)
        );
        def.getAdapterConfigRefs().forEach(ref ->
                hipIntegrationRuntimeService.updateThrottle(serviceManagerName, hipIntegrationName, version, ref.getId(), settings)
        );
        logger.info("Applied throttle for HIPIntegration: {}:{}", hipIntegrationName, version);
    }

    public void removeThrottle(String hipIntegrationName, String version) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        def.getAdapterConfigRefs().forEach(ref ->
                clusterCoordinationService.removeThrottle(def, ref)
        );
        def.getAdapterConfigRefs().forEach(ref ->
                hipIntegrationRuntimeService.updateThrottle(serviceManagerName, hipIntegrationName, version, ref.getId(), null)
        );
        logger.info("Removed throttle for HIPIntegration: {}:{}", hipIntegrationName, version);
    }

    public int strictOrderManualRelease(String integrationName, String version, String stepPropertyRef, List<String> orderingKeyValues, Long uptoSeq) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(integrationName, version);
        if (def == null) throw new IllegalArgumentException("Integration not found: " + integrationName + ":" + version);
        StrictOrderConfig config = def.getConfig(stepPropertyRef, StrictOrderConfig.class);
        if (config == null) {
            throw new IllegalArgumentException("StrictOrderConfig not found for step: " + stepPropertyRef);
        }
        List<Message<?>> released = strictOrderProcessorFlowStep.manualRelease(def, config, orderingKeyValues, uptoSeq);
        for (Message<?> m : released) {
            processHIPIntegrationMessage(integrationName, version, m);
           // wiretapService.tap(m, def, "StrictOrderManualRelease", "info", "Manually released message from strict order buffer.");
        }
        return released.size();
    }

    public void onClusterEvent(HIPClusterEvent event) {
        // Adapter events
        if (event.isAdapterTarget()) {
            String integrationName = event.getIntegrationName();
            String version = event.getIntegrationVersion();
            String adapterId = event.getTargetId();
            HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(integrationName, version);

            // Handle case where integration definition is null (e.g., during unregistration)
            if (def == null) {
                logger.debug("Integration definition not found for cluster event {}:{}:{} - integration may have been unregistered",
                           integrationName, version, event.getEventType());
                return;
            }

            AdapterConfigRef ref = def.getAdapterConfigRefs().stream()
                    .filter(r -> r.getId().equals(adapterId))
                    .findFirst()
                    .orElse(null);
            if (ref == null) return;
            InputAdapterStrategy strat = serviceManager.getInputAdapterStrategy(ref.getType());
            if (strat == null) return;

            switch (event.getEventType()) {
                case "PAUSE" -> strat.pause(def, ref);
                case "RESUME" -> strat.resume(def, ref);
                case "THROTTLE_UPDATE" -> logger.info("Throttle update event for {}:{}:{}", integrationName, version, adapterId);
                case "REFILL" -> logger.info("Refill event for {}:{}:{}", integrationName, version, adapterId);
                case "UNREGISTERED" -> logger.info("Unregistration event received for {}:{} - ignoring as integration is being removed", integrationName, version);
                default -> logger.debug("Unhandled adapter cluster event: {}", event.getEventType());
            }
        }

        // Handler events
        if (event.isHandlerTarget()) {
            String integrationName = event.getIntegrationName();
            String version = event.getIntegrationVersion();
            String handlerId = event.getTargetId();
            HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(integrationName, version);

            // Handle case where integration definition is null (e.g., during unregistration)
            if (def == null) {
                logger.debug("Integration definition not found for cluster event {}:{}:{} - integration may have been unregistered",
                           integrationName, version, event.getEventType());
                return;
            }

            HandlerConfigRef ref = def.getHandlerConfigRefs().stream()
                    .filter(r -> r.getId().equals(handlerId))
                    .findFirst()
                    .orElse(null);
            if (ref == null) return;
            HandlerStrategy strat = serviceManager.getHandlerStrategy(ref.getType());
            if (strat == null) return;

            switch (event.getEventType()) {
                case "PAUSE" -> strat.pause(def, ref);
                case "RESUME" -> strat.resume(def, ref);
                case "SHUTDOWN" -> strat.shutdown(def, ref);
                case "UNREGISTERED" -> logger.info("Unregistration event received for {}:{} - ignoring as integration is being removed", integrationName, version);
                default -> logger.debug("Unhandled handler cluster event: {}", event.getEventType());
            }
        }
    }

    public List<HIPIntegrationInfo> getAllHIPIntegrationsWithStatus() {
        return serviceManager.getAllDefinitions().stream()
                .map(def -> {
                    IntegrationStatus status = hipIntegrationRuntimeService.getHIPIntegrationStatus(
                            serviceManagerName, def.getHipIntegrationName(), def.getVersion());
                    return new HIPIntegrationInfo(def.getHipIntegrationName(), def.getVersion(), status);
                })
                .collect(Collectors.toList());
    }

    private HandlerConfigRef findHandlerByRole(HIPIntegrationDefinition def, String role) {
        if (role == null) return null;
        return def.getHandlerConfigRefs().stream()
                .filter(ref -> role.equalsIgnoreCase(ref.getRole()))
                .findFirst()
                .orElse(null);
    }

    public static class HIPIntegrationInfo {
        private final String hipIntegrationName;
        private final String version;
        private final IntegrationStatus status;

        public HIPIntegrationInfo(String hipIntegrationName, String version, IntegrationStatus status) {
            this.hipIntegrationName = hipIntegrationName;
            this.version = version;
            this.status = status;
        }
        public String getHipIntegrationName() { return hipIntegrationName; }
        public String getVersion() { return version; }
        public IntegrationStatus getStatus() { return status; }
    }

    public void triggerSftpForcePoll(String integrationName, String version, String adapterId) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(integrationName, version);
        AdapterConfigRef ref = def.getAdapterConfigRefs().stream()
                .filter(r -> r.getId().equals(adapterId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Adapter not found: " + adapterId));

        InputAdapterStrategy strategy = serviceManager.getInputAdapterStrategyMap().get(ref.getType());
        if (strategy instanceof DynamicSFTPInputAdapter sftpAdapter) {
            sftpAdapter.triggerForcePoll(integrationName, version, adapterId);
        }
        hipIntegrationRuntimeService.recordSftpCallback(serviceManagerName, integrationName, version, adapterId);
    }

    @PostConstruct
    public void subscribeClusterEvents() {
        clusterCoordinationService.registerClusterEventListener(event -> {
            if (recentClusterEvents.size() >= MAX_EVENT_HISTORY) {
                recentClusterEvents.pollFirst();
            }
            recentClusterEvents.addLast(event);
        });
    }

    public void shutdownHandler(String name, String version, String handlerRef) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(name, version);
        if (def == null) {
            logger.warn("shutdownHandler: Integration not found {}:{}", name, version);
            return;
        }
        HandlerConfigRef ref = def.getHandlerConfigRefs().stream()
                .filter(r -> r.getPropertyRef().equals(handlerRef) || r.getId().equals(handlerRef))
                .findFirst()
                .orElse(null);
        if (ref == null) {
            logger.warn("shutdownHandler: Handler ref {} not found in {}:{}", handlerRef, name, version);
            return;
        }
        HandlerStrategy strat = serviceManager.getHandlerStrategy(ref.getType());
        if (strat != null) {
            strat.shutdown(def, ref);
        }
        clusterCoordinationService.shutdownHandler(def, ref);
        logger.info("Handler {} shutdown for {}:{}", handlerRef, name, version);
    }

    public void shutdownHIPIntegration(String name, String version) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(name, version);
        if (def == null) {
            logger.warn("shutdownHIPIntegration: Integration not found {}:{}", name, version);
            return;
        }
        serviceManager.getInputAdapterStrategyMap().values().forEach(strat -> strat.pause(def));
        for (HandlerStrategy strat : serviceManager.getHandlerStrategyMap().values()) {
            for (HandlerConfigRef ref : def.getHandlerConfigRefs()) {
                strat.pause(def, ref);
            }
        }
        hipIntegrationRuntimeService.updateHIPIntegrationStatus(serviceManagerName, name, version, IntegrationStatus.PAUSED);
        def.getAdapterConfigRefs().forEach(ref -> clusterCoordinationService.shutdownAdapter(def, ref));
        def.getHandlerConfigRefs().forEach(ref -> clusterCoordinationService.shutdownHandler(def, ref));
        logger.info("HIPIntegration {}:{} shutdown initiated.", name, version);
    }


    public void refreshIntegrationRules(String serviceManagerName, String integrationName, String version) {
        ruleCache.refreshIntegrationRules(serviceManagerName, integrationName, version);
    }

    public void refreshExplicitRules(List<RuleRef> ruleRefs) {
        ruleCache.refreshExplicitRules(ruleRefs);
    }
    /*public void refreshRulesCache(List<RuleRef> ruleRefs) {
        if (ruleRefs != null && !ruleRefs.isEmpty()) {
            ruleCache.preloadRules(ruleRefs);
        }
    }*/

    public List<HIPClusterEvent> getRecentClusterEvents() {
        return new ArrayList<>(recentClusterEvents);
    }
    public void pauseAdapter(String hipIntegrationName, String version, String adapterRef) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        if (def == null) {
            logger.warn("pauseAdapter: Integration not found {}:{}", hipIntegrationName, version);
            return;
        }
        AdapterConfigRef ref = def.getAdapterConfigRefs().stream()
                .filter(r -> r.getPropertyRef().equals(adapterRef) || r.getId().equals(adapterRef))
                .findFirst()
                .orElse(null);
        if (ref != null) {
            InputAdapterStrategy strat = serviceManager.getInputAdapterStrategy(ref.getType());
            if (strat != null) strat.pause(def, ref);
            if (clusterCoordinationService != null)
                clusterCoordinationService.pause(def, ref);
            logger.info("Adapter paused: {} in {}:{}", adapterRef, hipIntegrationName, version);
        } else {
            logger.warn("pauseAdapter: Adapter ref {} not found in {}:{}", adapterRef, hipIntegrationName, version);
        }
    }

    public void resumeAdapter(String hipIntegrationName, String version, String adapterRef) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        if (def == null) {
            logger.warn("resumeAdapter: Integration not found {}:{}", hipIntegrationName, version);
            return;
        }
        AdapterConfigRef ref = def.getAdapterConfigRefs().stream()
                .filter(r -> r.getPropertyRef().equals(adapterRef) || r.getId().equals(adapterRef))
                .findFirst()
                .orElse(null);
        if (ref != null) {
            InputAdapterStrategy strat = serviceManager.getInputAdapterStrategy(ref.getType());
            if (strat != null) strat.resume(def, ref);
            if (clusterCoordinationService != null)
                clusterCoordinationService.resume(def, ref);
            logger.info("Adapter resumed: {} in {}:{}", adapterRef, hipIntegrationName, version);
        } else {
            logger.warn("resumeAdapter: Adapter ref {} not found in {}:{}", adapterRef, hipIntegrationName, version);
        }
    }

// ==== Handler Pause/Resume ====

    public void pauseHandler(String hipIntegrationName, String version, String handlerRef) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        if (def == null) {
            logger.warn("pauseHandler: Integration not found {}:{}", hipIntegrationName, version);
            return;
        }
        HandlerConfigRef ref = def.getHandlerConfigRefs().stream()
                .filter(r -> r.getPropertyRef().equals(handlerRef) || r.getId().equals(handlerRef))
                .findFirst()
                .orElse(null);
        if (ref != null) {
            HandlerStrategy strat = serviceManager.getHandlerStrategy(ref.getType());
            if (strat != null) strat.pause(def, ref);
            if (clusterCoordinationService != null)
                clusterCoordinationService.pauseHandler(def, ref);
            logger.info("Handler paused: {} in {}:{}", handlerRef, hipIntegrationName, version);
        } else {
            logger.warn("pauseHandler: Handler ref {} not found in {}:{}", handlerRef, hipIntegrationName, version);
        }
    }

    public void resumeHandler(String hipIntegrationName, String version, String handlerRef) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        if (def == null) {
            logger.warn("resumeHandler: Integration not found {}:{}", hipIntegrationName, version);
            return;
        }
        HandlerConfigRef ref = def.getHandlerConfigRefs().stream()
                .filter(r -> r.getPropertyRef().equals(handlerRef) || r.getId().equals(handlerRef))
                .findFirst()
                .orElse(null);
        if (ref != null) {
            HandlerStrategy strat = serviceManager.getHandlerStrategy(ref.getType());
            if (strat != null) strat.resume(def, ref);
            if (clusterCoordinationService != null)
                clusterCoordinationService.resumeHandler(def, ref);
            logger.info("Handler resumed: {} in {}:{}", handlerRef, hipIntegrationName, version);
        } else {
            logger.warn("resumeHandler: Handler ref {} not found in {}:{}", handlerRef, hipIntegrationName, version);
        }
    }

    public HIPIntegrationDefinition mapReqToDefinition(HIPIntegrationRequest request) {
        HIPIntegrationDefinition def = new HIPIntegrationDefinition();

        // 1. Set simple fields
        def.setHipIntegrationName(request.getHipIntegrationName());
        def.setServiceManagerName(request.getServiceManagerName());
        def.setVersion(request.getVersion());
        def.setOwner(request.getOwner());
        def.setTags(request.getTags());
        def.setBusinessFlowName(request.getBusinessFlowName());
        //def.setDescription(request.getDescription());
        def.setThrottleSettings(request.getThrottleSettings());
        def.setBusinessFlowType(request.getBusinessFlowType());
        def.setBusinessFlowVersion(request.getBusinessFlowVersion());
        def.setHipIntegrationType(request.getHipIntegrationType());
        return def;
    }

    public HIPIntegrationDefinition mapInputToDefinition(String integrationName, String version, String serviceManagerName) {
		HIPIntegrationDefinition def = new HIPIntegrationDefinition();
		def.setHipIntegrationName(integrationName);
		def.setServiceManagerName(serviceManagerName);
		def.setVersion(version);
		return def;

    }
}