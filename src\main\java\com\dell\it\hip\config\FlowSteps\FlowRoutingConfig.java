package com.dell.it.hip.config.FlowSteps;

import java.util.List;

import com.dell.it.hip.config.rules.RuleEnabledStepConfig;
import com.dell.it.hip.config.rules.RuleRef;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
@Data
public class FlowRoutingConfig extends FlowStepConfig implements RuleEnabledStepConfig {
	@JsonProperty("isDbBacked")
    private boolean isDbBacked;

    @Override
    public List<RuleRef> getRuleRefs() {
        return null;
    }
    public boolean isDbBacked() { return isDbBacked; }
    public void setDbBacked(boolean dbBacked) { this.isDbBacked = dbBacked;}
}