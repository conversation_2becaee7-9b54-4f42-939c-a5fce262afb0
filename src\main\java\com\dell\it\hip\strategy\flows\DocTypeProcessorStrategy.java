package com.dell.it.hip.strategy.flows;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.AttributeMapping;
import com.dell.it.hip.config.FlowSteps.DocTypeConfig;
import com.dell.it.hip.config.FlowSteps.DocTypeIdentifier;
import com.dell.it.hip.config.FlowSteps.DocTypeProcessorStepConfig;
import com.dell.it.hip.config.FlowSteps.DocTypeRuleOperator;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.ValidationConfig;
import com.dell.it.hip.util.dataformatUtils.CsvUtil;
import com.dell.it.hip.util.dataformatUtils.JsonUtil;
import com.dell.it.hip.util.dataformatUtils.RegexUtil;
import com.dell.it.hip.util.dataformatUtils.StaediUtil;
import com.dell.it.hip.util.dataformatUtils.XmlUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.redis.HIPRedisKeyUtil;
import com.dell.it.hip.util.validation.MessageFormatDetector;
import com.dell.it.hip.util.validation.SchemaValidator;
import com.dell.it.hip.util.validation.StructuralValidator;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component("docTypeProcessor")
public class DocTypeProcessorStrategy implements FlowStepStrategy {

    @Autowired private StringRedisTemplate redisTemplate;
    @Autowired private WiretapService wiretapService;
    @Autowired private SchemaValidator schemaValidator;

    @Override
    public String getType() {
        return "docTypeProcessor";
    }

    @Override
    public List<Message<?>> executeStep(
            Message<?> message,
            FlowStepConfigRef stepConfigRef,
            HIPIntegrationDefinition def) {

        DocTypeProcessorStepConfig config = def.getConfig(stepConfigRef.getPropertyRef(), DocTypeProcessorStepConfig.class);

        String payload = (message.getPayload() instanceof byte[])
                ? new String((byte[]) message.getPayload())
                : message.getPayload().toString();

        String format = MessageFormatDetector.detect(payload);
        MessageBuilder<?> builder = MessageBuilder.fromMessage(message).setHeader("HIP.dataFormat", format)
				.setHeader("HIP.stepProcessedAt", Instant.now().toString());
        if ("UNKNOWN".equalsIgnoreCase(format)) {
            wiretapError(message, def, stepConfigRef, "Unknown/unsupported data format: " + payload);
            if (config.isTerminateOnUnknownFormat()) return List.of();
        }

        List<String> docTypesToCheck = config.getSupportedDocTypesPerFormat() != null
                ? config.getSupportedDocTypesPerFormat().getOrDefault(format, Collections.emptyList())
                : Collections.emptyList();

        DocTypeConfig matchedConfig = null;
        List<String> detectedDocType = new ArrayList<>();

        // --- DocType Identification ---
        for (String candidateKey : docTypesToCheck) { // candidateKey is "INVOICE:1.0"
            DocTypeConfig candidateCfg = fetchDocTypeConfig(candidateKey);
            if (candidateCfg == null) continue;

            boolean matches = matchesDocType(payload, format, candidateCfg);
            if (matches) {
				matchedConfig = candidateCfg;
				detectedDocType.add(candidateKey);
				builder.setHeader("HIP.documentType", detectedDocType);
				// --- Build output message with headers ---
				try {
					if (matchedConfig != null && matchedConfig.getAttributeMappings() != null) {
						Map<String, Object> attrs = extractAttributes(payload, format, matchedConfig.getAttributeMappings(),candidateKey);
						builder.copyHeaders(attrs);
					}
					if (config.getAttributeMappings() != null) {
						Map<String, Object> extraAttrs = extractAttributes(payload, format, config.getAttributeMappings(),candidateKey);
						builder.copyHeaders(extraAttrs);
					}
				} catch (Exception e) {
					wiretapError(message, def, stepConfigRef, "Attribute extraction failed: " + e.getMessage());
					return List.of();
				}
				break;
			}
        }

        // --- Handle no docType match ---
        if (matchedConfig == null) {
            if (config.isAllowGenericDocType() && config.getGenericDocType() != null) {
                detectedDocType.add(config.getGenericDocType());
            } else {
                wiretapError(message, def, stepConfigRef, "No matching docType, and generic not allowed.");
                return List.of();
            }
        }

        // --- Validation FIRST ---
        boolean isValid = true;
        if (matchedConfig != null && matchedConfig.getValidation() != null) {
            isValid = performValidation(payload, format, matchedConfig.getValidation(), message, def, stepConfigRef);
        } else if (detectedDocType != null && detectedDocType.contains(config.getGenericDocType())) {
            if (config.getValidation() != null) {
                isValid = performValidation(payload, format, config.getValidation(), message, def, stepConfigRef);
            } else {
                isValid = StructuralValidator.validate(payload, format);
                if (!isValid) wiretapError(message, def, stepConfigRef, "Generic structural validation failed");
            }
        }
        if (!isValid) return List.of();

        wiretapAudit(message, def, stepConfigRef, format, detectedDocType, matchedConfig);

        return List.of(builder.build());
    }

    // ====== HELPERS ======

    protected DocTypeConfig fetchDocTypeConfig(String docTypeKey) {
        try {
            String redisKey = HIPRedisKeyUtil.docTypeKey(docTypeKey);
            String rawJson = redisTemplate.opsForValue().get(redisKey);
            //String rawJson = redisTemplate.opsForValue().get(docTypeKey);
            if (rawJson == null) return null;
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(rawJson, DocTypeConfig.class);
        } catch (Exception e) {
            return null;
        }
    }

    protected boolean matchesDocType(String payload, String format, DocTypeConfig cfg) {
        if (cfg.getDocTypeIdentifiers() == null || cfg.getDocTypeIdentifiers().isEmpty()) return false;
        boolean all = cfg.getDocTypeRuleOperator() == DocTypeRuleOperator.ALL;
        for (DocTypeIdentifier id : cfg.getDocTypeIdentifiers()) {
            String actualVal = extractSingleValue(payload, format, id.getDerivedFrom(), id.getExpression());
            boolean matches = (id.getExpectedValue() == null)
                    ? (actualVal != null && !actualVal.isEmpty())
                    : id.getExpectedValue().equals(actualVal);
            if (all && !matches) return false;
            if (!all && matches) return true;
        }
        return all;
    }

    protected String extractSingleValue(String payload, String format, String derivedFrom, String expression) {
        switch (derivedFrom.toUpperCase()) {
            case "JSONPATH":
                return JsonUtil.extractField(payload, expression);
            case "XPATH":
                try { return XmlUtil.extractFieldFlexible(payload, expression, null); } catch (Exception ex) { return null; }
            case "CSV":
                return CsvUtil.extractField(payload, expression);
            case "REGEX":
                return RegexUtil.extract(payload, expression);
            case "EDI":
                return StaediUtil.extractField(payload, expression);
            default:
                return null;
        }
    }

	protected Map<String, Object> extractAttributes(String payload, String format, List<AttributeMapping> mappings,String docTypeKey) {
        Map<String, Object> out = new HashMap<>();
        if (mappings == null) return out;
        for (AttributeMapping mapping : mappings) {
            String value = extractSingleValue(payload, format, mapping.getDerivedFrom(), mapping.getExpression());
            if(!CollectionUtils.isEmpty(mapping.getUsage()))
            	for (String usedIn : mapping.getUsage()) {
            	out.put("HIP."+docTypeKey+"." + usedIn + "." + mapping.getAttributeName(), value);
            }
        }
        return out;
    }
    
    

    protected boolean performValidation(String payload, String format, ValidationConfig validation, Message<?> msg, HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        boolean valid = true;
        if (validation.isStructural()) {
            valid = StructuralValidator.validate(payload, format);
            if (!valid) wiretapError(msg, def, ref, "Structural validation failed");
        }
        if (valid && validation.isSchema() && validation.getSchemaKey() != null) {
            valid = schemaValidator.validate(payload, format, validation.getSchemaKey());
            if (!valid) wiretapError(msg, def, ref, "Schema validation failed for schemaKey: " + validation.getSchemaKey());
        }
        return valid;
    }

    protected void wiretapError(Message<?> message, HIPIntegrationDefinition def, FlowStepConfigRef ref, String msg) {
        if (wiretapService != null)
            wiretapService.tap(message, def, ref, "error", msg);
    }

    protected void wiretapAudit(Message<?> message, HIPIntegrationDefinition def, FlowStepConfigRef ref, String format, List<String> docTypes, DocTypeConfig docTypeConfig) {
        if (wiretapService != null) {
            Map<String, Object> auditDetails = new HashMap<>();
            auditDetails.put("dataFormat", format);
            auditDetails.put("docType", docTypes);
            //auditDetails.put("docTypeVersion", docTypeVersion != null ? docTypeVersion : (docTypeConfig != null ? docTypeConfig.getVersion() : null));
            auditDetails.put("integration", def.getHipIntegrationName());
            auditDetails.put("integrationVersion", def.getVersion());
            auditDetails.put("flowStep", ref.getPropertyRef());
            wiretapService.tap(message, def, ref, "audit", "DocType detection result"+ auditDetails);
        }
    }
}