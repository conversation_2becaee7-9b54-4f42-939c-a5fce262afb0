package com.dell.it.hip.config;
import java.io.Serializable;
import java.util.List;

import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.util.ThrottleSettings;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
@JsonIgnoreProperties(ignoreUnknown = true)
public class HIPIntegrationRequest implements Serializable {
  
	private String hipIntegrationName;
    private String version;
    private String hipIntegrationType;

    private String businessFlowType;
    private String owner;
    private String businessFlowName;

    private String businessFlowVersion;
    private String tags;

    private List<AdapterConfigRef> adapters;
    private List<FlowStepConfigRef> flowSteps;
    private List<HandlerConfigRef> handlers;

    private List<String> propertySheets;
    private ThrottleSettings throttleSettings;
    private String serviceManagerName;
    private String status;


    // other business-specific properties

    // Getters and setters

    public String getHipIntegrationName() { return hipIntegrationName; }
    public void setHipIntegrationName(String hipIntegrationName) { this.hipIntegrationName = hipIntegrationName; }

    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }

    public String getOwner() { return owner; }
    public void setOwner(String owner) { this.owner = owner; }

    public String getBusinessFlowName() { return businessFlowName; }
    public void setBusinessFlowName(String businessFlowName) { this.businessFlowName = businessFlowName; }

    public String getTags() { return tags; }
    public void setTags(String tags) { this.tags = tags; }

    public List<AdapterConfigRef> getAdapters() { return adapters; }
    public void setAdapters(List<AdapterConfigRef> adapters) { this.adapters = adapters; }

    public List<FlowStepConfigRef> getFlowSteps() { return flowSteps; }
    public void setFlowSteps(List<FlowStepConfigRef> flowSteps) { this.flowSteps = flowSteps; }

    public List<HandlerConfigRef> getHandlers() { return handlers; }
    public void setHandlers(List<HandlerConfigRef> handlers) { this.handlers = handlers; }

    public List<String> getPropertySheets() { return propertySheets; }
    public void setPropertySheets(List<String> propertySheets) { this.propertySheets = propertySheets; }

    public ThrottleSettings getThrottleSettings() { return throttleSettings; }
    public void setThrottleSettings(ThrottleSettings throttleSettings) { this.throttleSettings = throttleSettings; }

    public void setServiceManagerName(String serviceManagerName) { this.serviceManagerName = serviceManagerName; }
    public String getServiceManagerName() { return serviceManagerName; }

    public String getStatus() { return status;
    }
    public void setStatus(String status) {this.status = status; }

    public String getHipIntegrationType() {
        return hipIntegrationType;
    }

    public void setHipIntegrationType(String hipIntegrationType) {
        this.hipIntegrationType = hipIntegrationType;
    }

    public String getBusinessFlowVersion() {
        return businessFlowVersion;
    }

    public void setBusinessFlowVersion(String businessFlowVersion) {
        this.businessFlowVersion = businessFlowVersion;
    }

    public String getBusinessFlowType() {
        return businessFlowType;
    }

    public void setBusinessFlowType(String businessFlowType) {
        this.businessFlowType = businessFlowType;
    }

}