package com.dell.it.hip.config.Handlers;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicKafkaHandlerConfig extends HandlerConfig{
    // Unique ID for this handler config (used as propertyRef)
    private String configRef;

    // Core Kafka settings
    @JsonProperty("kafka.producer.bootstrapServers")
    private String bootstrapServers;
    
    @JsonProperty("kafka.producer.topic")
    private String topic;

    @JsonProperty("kafka.producer.client.id")
    private String clientId;

    @JsonProperty("kafka.producer.username")
	private String username;

	@JsonProperty("kafka.producer.password")
	private String password;

    // Security
	@JsonProperty("kafka.producer.securityProtocol")
    private String securityProtocol;         // PLAINTEXT, SSL, SASL_PLAINTEXT, SASL_SSL
	
	@JsonProperty("kafka.producer.saslMechanism")
    private String saslMechanism;            // PLAIN, SCRAM-SHA-256, SCRAM-SHA-512, GSSAPI, etc.
	
	@JsonProperty("kafka.producer.sasljaasconfig")
    private String sasljaasconfig;           // e.g., "org.apache.kafka.common.security.plain.PlainLoginModule required username='...' password='...';"
   
    @JsonProperty("kafka.producer.sslTruststoreLocation")
    private String sslTruststoreLocation;
    
    @JsonProperty("kafka.producer.sslTruststorePassword")
    private String sslTruststorePassword;

    @JsonProperty("kafka.producer.ssl.keystore.location")
    private String sslKeystoreLocation;

    @JsonProperty("kafka.producer.ssl.keystore.password")
    private String sslKeystorePassword;

    @JsonProperty("kafka.producer.ssl.key.password")
    private String sslKeyPassword;
    
    @JsonProperty("kafka.producer.ssltruststoretype")
    private String ssltruststoretype;
    
    @JsonProperty("kafka.producer.protocols")
    private String protocols;

    // Optimization/tuning
    @JsonProperty("kafka.producer.acks")
    private Integer acks;                    // 0, 1, all

    @JsonProperty("kafka.producer.batch.size")
    private Integer batchSize;               // bytes, e.g., 16384

    @JsonProperty("kafka.producer.linger.ms")
    private Integer lingerMs;                // ms, e.g., 5

    @JsonProperty("kafka.producer.buffer.memory")
    private Integer bufferMemory;            // bytes, e.g., 33554432

    @JsonProperty("kafka.producer.retries")
    private Integer retries;

    @JsonProperty("kafka.producer.max.in.flight.requests.per.connection")
    private Integer maxInFlightRequestsPerConnection;

    @JsonProperty("kafka.producer.delivery.timeout.ms")
    private Integer deliveryTimeoutMs;

    @JsonProperty("kafka.producer.request.timeout.ms")
    private Integer requestTimeoutMs;

    @JsonProperty("kafka.producer.enable.idempotence")
    private Boolean enableIdempotence;

    @JsonProperty("kafka.producer.compression.type")
    private Integer compressionType;         // NONE, GZIP, SNAPPY, LZ4, ZSTD (string or int)

    @JsonProperty("kafka.producer.gzip.enabled")
    private Boolean gzipEnabled;             // Explicit flag for your app logic (separate from Kafka compression.type)

    // Application-level
    @JsonProperty("kafka.producer.parameters")
    private Map<String, Object> parameters;  // Any additional/custom configs (header filters, etc.)

    // Getters and setters for all fields
    // ...
    // (Omitted for brevity; use your IDE to generate)
}