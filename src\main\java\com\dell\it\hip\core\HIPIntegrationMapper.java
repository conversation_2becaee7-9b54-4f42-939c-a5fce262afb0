package com.dell.it.hip.core;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import com.dell.it.hip.config.FlowSteps.DocTypeProcessorStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.core.registry.ConfigClassRegistry;
import com.dell.it.hip.util.PropertySheetFetcher;
import com.dell.it.hip.util.ThrottleSettings;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
public class HIPIntegrationMapper {

    private final ObjectMapper objectMapper;
    private final PropertySheetFetcher propertySheetFetcher;
    private final ConfigClassRegistry configClassRegistry;

    @Autowired
    public HIPIntegrationMapper(ObjectMapper objectMapper,
                                PropertySheetFetcher propertySheetFetcher,
                                ConfigClassRegistry configClassRegistry) {
        this.objectMapper = objectMapper;
        this.propertySheetFetcher = propertySheetFetcher;
        this.configClassRegistry = configClassRegistry;
    }

    public HIPIntegrationDefinition mapToDefinition(HIPIntegrationRequest request) throws Exception {
        HIPIntegrationDefinition def = new HIPIntegrationDefinition();

        // 1. Set simple fields
        def.setHipIntegrationName(request.getHipIntegrationName());
        def.setServiceManagerName(request.getServiceManagerName());
        def.setVersion(request.getVersion());
        def.setOwner(request.getOwner());
        def.setTags(request.getTags());
        def.setBusinessFlowName(request.getBusinessFlowName());
        //def.setDescription(request.getDescription());
        def.setThrottleSettings(request.getThrottleSettings());
        def.setBusinessFlowType(request.getBusinessFlowType());
        def.setBusinessFlowVersion(request.getBusinessFlowVersion());
        def.setHipIntegrationType(request.getHipIntegrationType());


        // 2. Merge properties from propertySheets
        Map<String, Object> mergedProps = propertySheetFetcher.fetchAndMerge(request.getPropertySheets());

        // 3. Map Adapters, Handlers, Steps configs and build config map (propertyRef -> configObj)
        Map<String, Object> configMap = new HashMap<>();

        // Adapters
        List<AdapterConfigRef> adapterRefs = request.getAdapters();
        def.setAdapterConfigRefs(adapterRefs); // persist for reference (as JSON string if needed)
        for (AdapterConfigRef ref : adapterRefs) {
            Class<?> configClass = configClassRegistry.resolveAdapterConfigClass(ref.getType());
            Object config = buildConfigObject(ref.getPropertyRef(), mergedProps, configClass);
            configMap.put(ref.getPropertyRef(), config);
        }

        // Handlers
        List<HandlerConfigRef> handlerRefs = request.getHandlers();
        def.setHandlerConfigRefs(handlerRefs);
        for (HandlerConfigRef ref : handlerRefs) {
            Class<?> configClass = configClassRegistry.resolveHandlerConfigClass(ref.getType());
            Object config = buildConfigObject(ref.getPropertyRef(), mergedProps, configClass);
            configMap.put(ref.getPropertyRef(), config);
        }

        // Flow Steps
        List<FlowStepConfigRef> stepRefs = request.getFlowSteps();
        def.setFlowStepConfigRefs(stepRefs);
        for (FlowStepConfigRef ref : stepRefs) {
            Class<?> configClass = configClassRegistry.resolveStepConfigClass(ref.getType());
            Object config = buildConfigObject(ref.getPropertyRef(), mergedProps, configClass);
            configMap.put(ref.getPropertyRef(), config);
        }

        // 4. Attach merged config map to definition for all runtime use
        def.setConfigMap(configMap);

        // 5. Optionally, persist mergedProps (useful for debugging/support)
        def.setMergedProperties(mergedProps);

        // (If you want a context, build it here and set on def)
        // def.setHipIntegrationContext(new HIPIntegrationContext(mergedProps, configMap));

        return def;
    }

    // Helper to build config object of given class from mergedProps[propertyRef]
    private Object buildConfigObject(String propertyRef, Map<String, Object> mergedProps, Class<?> configClass) throws Exception{
        Object value = mergedProps.get(propertyRef);
        if (value == null) {
            // You may want to throw exception or return an empty config
            return null;
        }
        return objectMapper.readValue(value.toString(), configClass);
    }
    
    public HIPIntegrationRequestEntity toEntity(HIPIntegrationRequest request) {
        HIPIntegrationRequestEntity entity = new HIPIntegrationRequestEntity();
        entity.setServiceManagerName(request.getServiceManagerName());
        entity.setHipIntegrationName(request.getHipIntegrationName());
        entity.setVersion(request.getVersion());
        entity.setBusinessFlowName(request.getBusinessFlowName());
        entity.setTags(request.getTags());
        entity.setStatus(request.getStatus());
        // Serialize lists/maps to JSON
        entity.setAdaptersJson(saveJsonFields(request.getAdapters()));
        entity.setHandlersJson(saveJsonFields(request.getHandlers()));
        entity.setFlowStepsJson(saveJsonFields(request.getFlowSteps()));
        entity.setPropertySheetsJson(saveJsonFields(request.getPropertySheets()));
        entity.setThrottleSettingsJson(saveJsonFields(request.getThrottleSettings()));
        entity.setBusinessFlowType(saveJsonFields(request.getBusinessFlowType()));
        entity.setBusinessFlowVersion(saveJsonFields(request.getBusinessFlowVersion()));
        entity.setHipIntegrationType(saveJsonFields(request.getHipIntegrationType()));

        return entity;
    }
    public HIPIntegrationRequest toPojo(HIPIntegrationRequestEntity entity) {
        HIPIntegrationRequest request = new HIPIntegrationRequest();
       // request.setServiceManagerName(entity.getServiceManagerName());
        request.setHipIntegrationName(entity.getHipIntegrationName());
        request.setVersion(entity.getVersion());
        request.setBusinessFlowName(entity.getBusinessFlowName());
        request.setBusinessFlowType(entity.getBusinessFlowType());
        request.setBusinessFlowVersion(entity.getBusinessFlowVersion());
        request.setHipIntegrationType(entity.getHipIntegrationType());
        request.setTags(entity.getTags());
        request.setStatus(entity.getStatus());
        // Deserialize JSON back to objects
        request.setAdapters(loadJsonFields(entity.getAdaptersJson(), new TypeReference<List<AdapterConfigRef>>() {}));
        request.setHandlers(loadJsonFields(entity.getHandlersJson(), new TypeReference<List<HandlerConfigRef>>() {}));
        request.setFlowSteps(loadJsonFields(entity.getFlowStepsJson(), new TypeReference<List<FlowStepConfigRef>>() {}));
        request.setPropertySheets(loadJsonFields(entity.getPropertySheetsJson(), new TypeReference<List<String>>() {}));
        request.setThrottleSettings(loadJsonFields(entity.getThrottleSettingsJson(), ThrottleSettings.class));
        return request;
    }

    // Serialize object to JSON string
    public String saveJsonFields(Object value) {
        if (value == null) return null;
        try {
            return objectMapper.writeValueAsString(value);
        } catch (Exception e) {
            throw new RuntimeException("Error serializing field to JSON", e);
        }
    }

    // Deserialize JSON string to Java object
    public <T> T loadJsonFields(String json, TypeReference<T> typeRef) {
        if (json == null) return null;
        try {
            return objectMapper.readValue(json, typeRef);
        } catch (Exception e) {
            throw new RuntimeException("Error deserializing JSON field", e);
        }
    }
    // Overload for class type (for POJO, not collection)
    public <T> T loadJsonFields(String json, Class<T> type) {
        if (json == null) return null;
        try {
            return objectMapper.readValue(json, type);
        } catch (Exception e) {
            throw new RuntimeException("Error deserializing JSON field", e);
        }
    }
    
    public static void main(String[] args) throws Exception {
    	// JSON string with corrected field names to match DynamicIBMMQAdapterConfig
    	String value = "{\r\n"
    			+ "        \"supportedDocTypesPerFormat.JSON[0]\": \"testdoc:1\",\r\n"
    			+ "        \"supportedDocTypesPerFormat.JSON[1]\": \"testdoce1:1\"\r\n"
    			+ "      }";

    	ObjectMapper objectMapper = new ObjectMapper();
    	Class<?> configClass = Class.forName("com.dell.it.hip.config.FlowSteps.DocTypeProcessorStepConfig");

    	try {
    	    // Use readValue for JSON strings, not convertValue
    	    Object config = objectMapper.readValue(value, configClass);
    	    System.out.println("Successfully deserialized config: " + config);

    	    // Cast to the specific type to access properties
    	    DocTypeProcessorStepConfig docConfig =
    	        (DocTypeProcessorStepConfig) config;

    	    System.out.println("Supported Doc Types Per Format: " + docConfig.getSupportedDocTypesPerFormat());
    	    if (docConfig.getSupportedDocTypesPerFormat() != null &&
    	        docConfig.getSupportedDocTypesPerFormat().containsKey("JSON")) {
    	        System.out.println("JSON Doc Types: " + docConfig.getSupportedDocTypesPerFormat().get("JSON"));
    	    }
    	   /* System.out.println("Queue Name: " + ibmConfig.getQueueName());
    	    System.out.println("Channel: " + ibmConfig.getChannel());
    	    System.out.println("Connection Name: " + ibmConfig.getConnName());
    	    System.out.println("Username: " + ibmConfig.getUsername());
    	    System.out.println("Authentication Type: " + ibmConfig.getAuthenticationType());
    	    System.out.println("Encoding: " + ibmConfig.getEncoding());*/

    	} catch (Exception e) {
    	    System.err.println("Error deserializing JSON: " + e.getMessage());
    	    e.printStackTrace();
    	}
	}
}