package com.dell.it.hip.config.adapters;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicIBMMQAdapterConfig extends AdapterConfig {
    @JsonProperty("ibmmq.consumer.queueManager")
    private String queueManager;
    
    @JsonProperty("ibmmq.consumer.queue")
    private String queueName;
    
    @JsonProperty("ibmmq.consumer.channel")
    private String channel;
    
    @JsonProperty("ibmmq.consumer.connName")
    private String connName;
    
    @JsonProperty("ibmmq.consumer.auth.type")
    private String authenticationType;
    
    @JsonProperty("ibmmq.consumer.username")
    private String username;

    @JsonProperty("ibmmq.consumer.password")
    private String password;

    // SSL/TLS
    @JsonProperty("ibmmq.consumer.sslCipherSuite")
    private String sslCipherSuite;

    @JsonProperty("ibmmq.consumer.sslPeerName")
    private String sslPeerName;

    @JsonProperty("ibmmq.consumer.sslKeystore")
    private String sslKeystore;

    @JsonProperty("ibmmq.consumer.sslKeystorePassword")
    private String sslKeystorePassword;

    @JsonProperty("ibmmq.consumer.sslTruststore")
    private String sslTruststore;

    @JsonProperty("ibmmq.consumer.sslTruststorePassword")
    private String sslTruststorePassword;

    // Charset/encoding
    private Integer ccsid;      // MQ CCSID
    private Integer encoding;   // MQ encoding
    private boolean compressed = false;

    // Performance/concurrency (used properties only)
    private Integer concurrency;
    private Long receiveTimeout;
    private Long recoveryInterval;
    private Boolean transacted;

    // Consumer (used properties only)
    private String messageSelector;
    private List<String> headersToExtract;

    // Advanced (used properties only)
    private Map<String, ?> properties;

    // Override parent's properties method to provide specific type
    @Override
    public void setProperties(Map<String, ?> properties) {
        super.setProperties(properties);
    }

    // Convenience method for String-specific properties
    public void setStringProperties(Map<String, String> properties) {
        super.setProperties(properties);
    }

    // Getters and setters for all above...

    // ... (omitted for brevity)
}