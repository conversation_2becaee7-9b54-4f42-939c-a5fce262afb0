package com.dell.it.hip.strategy.adapters;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jms.listener.DefaultMessageListenerContainer;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.config.adapters.DynamicIBMMQAdapterConfig;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.ibm.mq.jakarta.jms.MQQueueConnectionFactory;
import com.ibm.msg.client.jakarta.wmq.WMQConstants;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import jakarta.jms.BytesMessage;
import jakarta.jms.ConnectionFactory;
import jakarta.jms.MessageListener;
import jakarta.jms.TextMessage;

@Component("ibmmqAdapter")
public class DynamicIbmmqInputAdapter extends AbstractDynamicInputAdapter {

    private static final Logger logger = LoggerFactory.getLogger(DynamicIbmmqInputAdapter.class);

    @Autowired
    private WiretapService wiretapService;
    @Autowired
    private OpenTelemetryPropagationUtil openTelemetryPropagationUtil;
    
    @Autowired
    private Tracer tracer;

    @Override
    public String getType() { return "ibmmqAdapter"; }

    @Override
    public void buildProducer(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        if (!getType().equals(ref.getType())) {
            return;
        }
        DynamicIBMMQAdapterConfig cfg = (DynamicIBMMQAdapterConfig) def.getConfigMap().get(ref.getPropertyRef());
        if (cfg == null) {
            throw new IllegalStateException("No config found for IBM MQ adapter ref: " + ref.getPropertyRef());
        }

        MQQueueConnectionFactory factory = new MQQueueConnectionFactory();
        try {
            factory.setQueueManager(cfg.getQueueManager());
            factory.setChannel(cfg.getChannel());
            if (cfg.getConnName() != null) {
                factory.setConnectionNameList(cfg.getConnName());
            }
            factory.setTransportType(WMQConstants.WMQ_CM_CLIENT);
            if (cfg.getCcsid() != null) {
                factory.setCCSID(cfg.getCcsid());
            }
            if (cfg.getEncoding() != null) {
                factory.setIntProperty(WMQConstants.WMQ_ENCODING, cfg.getEncoding());
            }
            if (cfg.getAuthenticationType() != null && !"none".equalsIgnoreCase(cfg.getAuthenticationType())) {
                if (cfg.getUsername() != null) {
                    factory.setStringProperty(WMQConstants.USERID, cfg.getUsername());
                }
                if (cfg.getPassword() != null) {
                    factory.setStringProperty(WMQConstants.PASSWORD, cfg.getPassword());
                }
            }
            // SSL/TLS config if present
            if (cfg.getSslCipherSuite() != null) {
                factory.setSSLCipherSuite(cfg.getSslCipherSuite());
            }
            if (cfg.getSslPeerName() != null) {
                factory.setSSLPeerName(cfg.getSslPeerName());
            }
        } catch (Exception ex) {
            logger.error("Error setting up IBM MQ ConnectionFactory: {}", ex.getMessage(), ex);
            throw new IllegalStateException("IBM MQ setup failed", ex);
        }

        DefaultMessageListenerContainer container = new DefaultMessageListenerContainer();
        container.setConnectionFactory((ConnectionFactory) factory);
        container.setDestinationName(cfg.getQueueName());
        if (cfg.getConcurrency() != null) container.setConcurrentConsumers(cfg.getConcurrency());
        if (cfg.getReceiveTimeout() != null) container.setReceiveTimeout(cfg.getReceiveTimeout());
        if (cfg.getRecoveryInterval() != null) container.setRecoveryInterval(cfg.getRecoveryInterval());
        if (cfg.getTransacted() != null) container.setSessionTransacted(cfg.getTransacted());
        if (cfg.getMessageSelector() != null) container.setMessageSelector(cfg.getMessageSelector());

     
        container.setMessageListener((MessageListener) jmsMsg -> {
            try {
                byte[] payload;
                Map<String, Object> headers = new HashMap<>();
                if (jmsMsg instanceof BytesMessage bytesMessage) {
                    payload = new byte[(int) bytesMessage.getBodyLength()];
                    bytesMessage.readBytes(payload);
                } else if (jmsMsg instanceof TextMessage textMessage) {
                    String charset = getJavaEncodingFromCcsid(cfg.getCcsid());
                    payload = textMessage.getText().getBytes(charset);
                } else {
                    logger.warn("Unsupported IBM MQ message type: {}", jmsMsg.getClass());
                    return;
                }
                // Extract headers from JMS message
                for (Enumeration<?> e = jmsMsg.getPropertyNames(); e.hasMoreElements(); ) {
                    String name = (String) e.nextElement();
                    if (cfg.getHeadersToExtract() == null || cfg.getHeadersToExtract().contains(name)) {
                        headers.put(name, jmsMsg.getObjectProperty(name));
                    }
                }
                if (cfg.getQueueName() != null)
                    headers.put("ibmmq_queue", cfg.getQueueName());
                // Decompress if needed
                if (cfg.isCompressed()) {
                    payload = CompressionUtil.decompress(payload);
                }
                Message<byte[]> inboundMsg = MessageBuilder.withPayload(payload)
                        .copyHeaders(headers)
                        .build();
                
                // Extract context from headers
                Context extractedContext = OpenTelemetryPropagationUtil.extractContextFromMessage(inboundMsg);

                // Start span with parent context
                Span span = tracer.spanBuilder("mq.receive")
                        .setSpanKind(SpanKind.CONSUMER)
                        .setParent(extractedContext)
                        .startSpan();


                try (Scope scope = span.makeCurrent()) {
                	
                    // Always propagate (or generate) traceparent
                    logger.info("Trace ID: {}", span.getSpanContext().getTraceId());
                    logger.info("Span ID: {}", span.getSpanContext().getSpanId());
                    logger.info("Is valid: {}", span.getSpanContext().isValid());
                    Message<?> msgWithTrace = openTelemetryPropagationUtil.injectTraceContext(inboundMsg);
                    MDC.put("traceId", span.getSpanContext().getTraceId());

                    // --- WIRETAP: Only for message received ---
                    wiretapService.tap(
                            msgWithTrace,
                            def,
                            ref,
                            "received",
                            "Message received from IBM MQ, queue=" + cfg.getQueueName()
                    );
                    MessageChannel inputChannel = getInputChannel(def);
                    // Send to next stage in flow (throttle, dedup, etc handled in abstract base)
                    processInboundMessage(def, ref, msgWithTrace, inputChannel);
                }finally {
                    span.end();
                }


            } catch (Exception ex) {
                logger.error("IBM MQ message handling error for queue={}, ref={}: {}", cfg.getQueueName(), ref.getId(), ex.getMessage(), ex);
                wiretapService.tap(null, def, ref, "error",
                        "IBM MQ message handling error: " + ex.getMessage());
            }
        });
        MessageChannel inputChannel = getInputChannel(def);
        IbmMqAdapterInstance instance = new IbmMqAdapterInstance(container, inputChannel, cfg.getQueueName(), ref);
        registerAdapterInstance(def, ref, instance);

        container.afterPropertiesSet();
        container.start();
        logger.info("IBM MQ listener started for queue={}, ref={}", cfg.getQueueName(), ref.getId());
    }

    @Override
    protected Message<?> toMessage(HIPIntegrationDefinition def, AdapterConfigRef ref, Object raw) {
        if (raw instanceof Message<?> msg) return msg;
        if (raw instanceof byte[] data) {
            return MessageBuilder.withPayload(data).build();
        }
        return MessageBuilder.withPayload(raw).build();
    }

    @Override
    protected void shutdownAdapterInstance(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof IbmMqAdapterInstance ibmInst) {
            ibmInst.container.stop();
            logger.info("IBM MQ container stopped for ref={}", ref.getId());
        }
    }

    @Override
    protected void startAdapterInstance(AdapterInstance instance) {
        if (instance instanceof IbmMqAdapterInstance ibmInst) {
            ibmInst.container.start();
            logger.info("IBM MQ container started for ref={}", ibmInst.ref.getId());
        }
    }

    @Override
    protected void stopAdapterInstance(AdapterInstance instance) {
        if (instance instanceof IbmMqAdapterInstance ibmInst) {
            ibmInst.container.stop();
            logger.info("IBM MQ container stopped for ref={}", ibmInst.ref.getId());
        }
    }

    @Override
    protected void doPause(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof IbmMqAdapterInstance ibmInst) {
            DefaultMessageListenerContainer container = ibmInst.container;
            if (container.isRunning()) {
                container.stop();
                logger.info("IBM MQ consumer paused for ref={}", ref.getId());
            }
        }
    }

    @Override
    protected void doResume(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof IbmMqAdapterInstance ibmInst) {
            DefaultMessageListenerContainer container = ibmInst.container;
            if (!container.isRunning()) {
                container.start();
                logger.info("IBM MQ consumer resumed for ref={}", ref.getId());
            }
        }
    }

    public static class IbmMqAdapterInstance extends AdapterInstance {
        final DefaultMessageListenerContainer container;
        final MessageChannel inputChannel;
        final String queueName;
        final AdapterConfigRef ref;

        public IbmMqAdapterInstance(
                DefaultMessageListenerContainer container,
                MessageChannel inputChannel,
                String queueName,
                AdapterConfigRef ref
        ) {
            this.container = container;
            this.inputChannel = inputChannel;
            this.queueName = queueName;
            this.ref = ref;
        }
    }

    private static String getJavaEncodingFromCcsid(Integer ccsid) {
        if (ccsid == null) return "UTF-8";
        switch (ccsid) {
            case 1208: return "UTF-8";
            case 819:  return "ISO-8859-1";
            // Add more supported CCSIDs as needed
            default:   return "UTF-8";
        }
    }
}