package com.dell.it.hip.config.FlowSteps;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Configuration for AttributeProcessor flow step.
 */
@JsonDeserialize(using = AttributeProcessorConfigDeserializer.class)
public class AttributeProcessorConfig extends FlowStepConfig{
    private String propertyRef; // Unique key in property sheet/configMap
    private List<AttributeMapping> attributeMappings;

    public String getPropertyRef() {
        return propertyRef;
    }
    public void setPropertyRef(String propertyRef) {
        this.propertyRef = propertyRef;
    }

    public List<AttributeMapping> getAttributeMappings() {
        return attributeMappings;
    }
    public void setAttributeMappings(List<AttributeMapping> attributeMappings) {
        this.attributeMappings = attributeMappings;
    }

    @Override
    public String toString() {
        return "AttributeProcessorConfig{" +
                "propertyRef='" + propertyRef + '\'' +
                ", attributeMappings=" + attributeMappings +
                '}';
    }

    /**
     * How an attribute is derived.
     */
    public enum DerivedFromType {
        FILENAME,
        ROOT_ELEMENT,
        DERIVED_FROM_PAYLOAD
    }

}

/**
 * Custom deserializer to handle dot notation with array indices like:
 * "attributeMappings[0].attributeName": "Receiver"
 * "attributeMappings[0].derivedFrom": "ELEMENT_IN_PAYLOAD"
 * "attributeMappings[0].required": "true"
 * "attributeMappings[0].usage": "Routing,Mapping,Logging"
 *
 * Converts them to proper List<AttributeMapping> structure.
 */
class AttributeProcessorConfigDeserializer extends JsonDeserializer<AttributeProcessorConfig> {

    private static final Pattern DOT_NOTATION_PATTERN =
        Pattern.compile("attributeMappings\\[(\\d+)\\]\\.(.+)");

    @Override
    public AttributeProcessorConfig deserialize(JsonParser parser, DeserializationContext context)
            throws IOException, JsonProcessingException {

        JsonNode node = parser.getCodec().readTree(parser);
        AttributeProcessorConfig config = new AttributeProcessorConfig();

        // Map to collect AttributeMapping data by index
        Map<Integer, Map<String, String>> attributeMappingData = new HashMap<>();

        // Iterate through all fields in the JSON
        Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            String fieldName = field.getKey();
            JsonNode fieldValue = field.getValue();

            // Check if this field matches our dot notation pattern
            Matcher matcher = DOT_NOTATION_PATTERN.matcher(fieldName);
            if (matcher.matches()) {
                int index = Integer.parseInt(matcher.group(1));  // e.g., 0, 1, 2
                String propertyName = matcher.group(2);  // e.g., "attributeName", "derivedFrom"
                String value = fieldValue.asText();

                // Get or create the map for this index
                Map<String, String> mappingData = attributeMappingData.computeIfAbsent(index, k -> new HashMap<>());
                mappingData.put(propertyName, value);
            } else {
                // Handle regular properties
                switch (fieldName) {
                    case "propertyRef":
                        config.setPropertyRef(fieldValue.asText());
                        break;
                    // Add other properties from parent FlowStepConfig class
                    case "type":
                        config.setType(fieldValue.asText());
                        break;
                    case "id":
                        config.setId(fieldValue.asText());
                        break;
                    case "role":
                        config.setRole(fieldValue.asText());
                        break;
                }
            }
        }

        // Convert the collected data into List<AttributeMapping>
        if (!attributeMappingData.isEmpty()) {
            List<AttributeMapping> attributeMappings = new ArrayList<>();

            // Process indices in order (0, 1, 2, ...)
            int maxIndex = attributeMappingData.keySet().stream().mapToInt(Integer::intValue).max().orElse(-1);
            for (int i = 0; i <= maxIndex; i++) {
                Map<String, String> mappingData = attributeMappingData.get(i);
                if (mappingData != null) {
                    AttributeMapping mapping = new AttributeMapping();

                    // Set properties from the collected data
                    if (mappingData.containsKey("attributeName")) {
                        mapping.setAttributeName(mappingData.get("attributeName"));
                    }
                    if (mappingData.containsKey("derivedFrom")) {
                        mapping.setDerivedFrom(mappingData.get("derivedFrom"));
                    }
                    if (mappingData.containsKey("expression")) {
                        mapping.setExpression(mappingData.get("expression"));
                    }
                    if (mappingData.containsKey("required")) {
                        // Convert string "true"/"false" to boolean
                        mapping.setRequired(Boolean.parseBoolean(mappingData.get("required")));
                    }
                    if (mappingData.containsKey("usage")) {
                        // Parse comma-separated string into List<String>
                        String usageStr = mappingData.get("usage");
                        if (usageStr != null && !usageStr.trim().isEmpty()) {
                            List<String> usageList = Arrays.asList(usageStr.split(","));
                            // Trim whitespace from each element
                            usageList = usageList.stream()
                                .map(String::trim)
                                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
                            mapping.setUsage(usageList);
                        }
                    }

                    attributeMappings.add(mapping);
                } else {
                    // Add null for missing indices to maintain order
                    attributeMappings.add(null);
                }
            }

            config.setAttributeMappings(attributeMappings);
        }

        return config;
    }
}